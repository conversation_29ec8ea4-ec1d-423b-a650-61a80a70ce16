<template>
  <div
    class="home_view"
    :style="deviceStore.isMobile ? 'background: #f6f6f6' : ''"
  >
    <swiper
      :modules="modules"
      :pagination="{
        clickable: true,
      }"
      :navigation="!deviceStore.isMobile"
      :slidesPerView="1"
      :spaceBetween="30"
      :loop="true"
      class="home_view_swiper"
      :class="{ 'mobile-swiper': deviceStore.isMobile }"
    >
      <swiper-slide v-for="(item, index) in slideshowImages" :key="index">
        <img
          :src="item.showUrl"
          class="home_view_banner"
          :style="
            deviceStore.isMobile ? 'height:100%;margin-bottom: -0.5rem;' : ''
          "
        />
      </swiper-slide>
    </swiper>
    <div
      class="home_view_product"
      :class="{ 'mobile-layout': deviceStore.isMobile }"
    >
      <div
        class="home_view_product_title"
        :style="deviceStore.isMobile ? 'flex-wrap: wrap;padding:2rem' : ''"
      >
        {{ t('home.title') }}
        <div
          class="home_view_product_title_btn"
          @click="onShowList()"
          :style="deviceStore.isMobile ? 'margin-top:1rem' : ''"
        >
          <div style="display: flex; align-items: center; white-space: nowrap">
            {{ t('home.shopmall') }}
          </div>
          <img
            src="@assets/arrow.png"
            class="home_view_product_title_btn_icon"
          />
        </div>
      </div>
      <div :style="deviceStore.isMobile ? 'padding:1.6rem' : ''">
        <goodsList
          :products="productList"
          :singleColumn="deviceStore.isMobile"
        />
      </div>
    </div>
    <div
      class="home_view_vedio"
      :style="
        deviceStore.isMobile
          ? 'padding:0;background:#f6f6f6;margin-top:2rem'
          : ''
      "
    >
      <video
        :src="videoUrl || ''"
        class="home_view_vedio_url"
        controls
        autoplay
        muted
        :loop="!deviceStore.isMobile"
      />
    </div>
    <div
      class="home_view_ledger"
      :style="deviceStore.isMobile ? 'padding:0;flex-wrap:wrap' : ''"
      v-if="deviceStore.isMobile"
    >
      <div
        class="home_view_ledger_main"
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          flex: 1;
          margin-top: 4rem;
          margin-bottom: 4rem;
        "
      >
        <div style="font-size: 2.2rem; font-weight: 500">
          {{ t('home.use') }}
          <span class="puper" style="color: #6e4aeb">Ledger</span>
          {{ t('home.wallet') }}
        </div>
        <div style="margin-top: 0.5rem; font-weight: 500">
          {{ t('home.tip') }}
        </div>
      </div>
      <img
        v-if="deviceStore.isMobile"
        src="@assets/h5/img_p3.png"
        class="home_view_ledger_img"
        :style="
          deviceStore.isMobile
            ? 'width:100%;margin-left: 2.5rem;height: 100%;'
            : ''
        "
      />
      <img v-else src="@assets/ledger.png" class="home_view_ledger_img" />
    </div>
    <div class="home_view_ledger" v-else>
      <img src="@assets/ledger.png" class="home_view_ledger_img" />
      <div class="home_view_ledger_main">
        <p>
          {{ t('home.use') }}
          <span class="puper">Ledger</span>
          {{ t('home.wallet') }}
        </p>
        <p style="font-size: 3rem">{{ t('home.tip') }}</p>
      </div>
    </div>
    <div
      class="home_view_banner"
      :style="deviceStore.isMobile ? 'height:100%' : ''"
    >
      <div
        class="home_view_banner_main"
        :style="
          deviceStore.isMobile
            ? 'padding:0 1.5rem;position:relative;top:0;background-color:#fff'
            : ''
        "
      >
        <div class="home_view_banner_main_left">
          <div
            class="puper"
            :style="
              deviceStore.isMobile ? 'font-size:2.2rem;padding-top:3rem' : ''
            "
          >
            {{ t('zhi_chi')
            }}<span style="color: #6e4aeb">{{ t('shu_qian_zhong') }}</span
            >{{ t('ying_bi_he_dai_bi') }}
          </div>
          <div
            class="desc"
            :style="
              deviceStore.isMobile ? 'font-size:1.4rem;margin-top:1rem' : ''
            "
          >
            {{ t('bi_te_bi_yi_tai_fang_usdtsolana_deng_deng') }}
          </div>
        </div>
        <!-- <div class="home_view_banner_main_right">
          <span class="title">查看所有支持的加密货币</span>
          <img
            src="@assets/arrow.png"
            class="home_view_banner_main_right_icon"
            @click="onMoreCoins"
          />
        </div> -->
      </div>
      <img
        v-if="deviceStore.isMobile"
        src="@assets/h5/img_p5.png"
        class="home_view_banner_img"
        :style="deviceStore.isMobile ? 'height:100%' : ''"
      />
      <img v-else src="@assets/banner.png" class="home_view_banner_img" />
    </div>

    <div
      class="home_view_introduce"
      :style="deviceStore.isMobile ? 'padding:0 1.6rem;margin-top:3.4rem' : ''"
    >
      <div
        class="home_view_introduce_title"
        :style="deviceStore.isMobile ? 'line-height:2.8rem' : ''"
      >
        <span>Ledger Live</span>{{ t('home.introduce') }}
      </div>
      <div class="home_view_introduce_desc">{{ t('home.desc') }}</div>
      <div
        class="home_view_introduce_list"
        :style="
          deviceStore.isMobile
            ? 'flex-wrap:wrap;gap:8.6rem;margin-bottom:6rem'
            : ''
        "
      >
        <div
          class="home_view_introduce_list_item"
          :style="deviceStore.isMobile ? 'width:100%' : ''"
        >
          <img
            src="@assets/icon1.png"
            class="home_view_introduce_list_item_icon"
          />
          <div class="home_view_introduce_list_item_name">
            {{ t('home.name1') }}
          </div>
          <div class="home_view_introduce_list_item_desc">
            {{ t('home.desc1') }}
          </div>
          <img
            src="@assets/img1.png"
            class="home_view_introduce_list_item_img"
            :style="deviceStore.isMobile ? 'width:100%' : ''"
          />
        </div>
        <div class="home_view_introduce_list_item">
          <img
            src="@assets/icon2.png"
            class="home_view_introduce_list_item_icon"
          />
          <div class="home_view_introduce_list_item_name">
            {{ t('home.name2') }}
          </div>
          <div class="home_view_introduce_list_item_desc">
            {{ t('home.desc2') }}
          </div>
          <img
            src="@assets/img2.png"
            class="home_view_introduce_list_item_img"
            :style="deviceStore.isMobile ? 'width:100%' : ''"
          />
        </div>
        <div class="home_view_introduce_list_item">
          <img
            src="@assets/icon3.png"
            class="home_view_introduce_list_item_icon"
          />
          <div class="home_view_introduce_list_item_name">
            {{ t('home.name3') }}
          </div>
          <div class="home_view_introduce_list_item_desc">
            {{ t('home.desc3') }}
          </div>
          <img
            src="@assets/img3.png"
            class="home_view_introduce_list_item_img"
            :style="deviceStore.isMobile ? 'width:100%' : ''"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { GetHomeInfo, GetLatestProducts } from '@api';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import goodsList from '@components/goodsList.vue';
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { currencyUpdated, languageUpdated } from '@utils/eventBus';
import { useDeviceStore } from '@/stores/device';

const { t, locale } = useI18n();
const router = useRouter();
const modules = ref([Navigation, Pagination]);
const deviceStore = useDeviceStore();

// 根据设备类型计算不同的商品展示数量
const displayProductCount = computed(() => {
  return deviceStore.isMobile ? 2 : 4;
});

const detail = ref({});
const homeData = ref({});
const slideshowImages = ref([]);
const productList = ref([]); // 新增：用于存储商品列表
const videoUrl = ref('');

// 根据当前语言获取图片链接
const getImageByLanguage = (imageObj) => {
  const currentLocale = locale.value;
  const languageMap = {
    zh: 'zh',
    zh_hk: 'hk',
    en: 'en',
    es: 'es',
    fr: 'fr',
    de: 'de',
    it: 'it',
    ja: 'ja',
    ko: 'ko',
    id: 'id',
    ru: 'ru',
  };

  const apiLanguage = languageMap[currentLocale] || 'en';
  let url = imageObj[apiLanguage] || imageObj['en'] || '';
  if (!url.startsWith('/')) {
    url = '/' + url;
  }
  return url;
};

// 处理轮播图数据
const processSlideshow = (modules) => {
  if (!modules || !Array.isArray(modules)) return;
  const slideshowModule = modules.filter(
    (module) => module.code === 'slideshow'
  );
  if (!slideshowModule.length) return;
  const images = slideshowModule.map((item) => item.content.images[0]);
  const videoData = images.find((x) => x.type == 'video');
  if (videoData && videoData.image) {
    const videoFormats = ['.mp4', '.mov', '.avi', '.webm', '.mkv'];
    for (const langKey in videoData.image) {
      const path = videoData.image[langKey];
      if (videoFormats.some((format) => path.toLowerCase().endsWith(format))) {
        videoUrl.value = `http://************/${
          path.startsWith('/') ? path.substring(1) : path
        }`;
        break;
      }
    }
  }

  slideshowImages.value = images
    .filter((x) => x.type != 'video')
    .map((img) => ({
      ...img,
      showUrl: `http://************${getImageByLanguage(img.image)}`,
    }));
};

const change = () => {
  locale.value = 'zh';
};

const onShowDetail = (id) => {
  router.push(`/goods/${id}`);
};

const onShowList = () => {
  router.push(`/goods`);
};

const onMoreCoins = () => {
  console.log('查看所有支持的加密货币');
};

const onSwiper = (swiper) => {
  console.log(swiper);
};

const onSlideChange = () => {
  console.log('slide change');
};

onMounted(() => {
  onLoadData();
  // 初始化设备检测
  deviceStore.initDevice();
});

// 监听币种更新事件
watch(currencyUpdated, () => {
  console.log('检测到币种更新，重新加载商品列表');
  loadLatestProducts();
});

// 监听语言更新事件
watch(languageUpdated, () => {
  console.log('检测到语言更新，重新加载首页数据');
  onLoadData();
});

const onLoadData = () => {
  // 获取首页数据
  GetHomeInfo()
    .then((res) => {
      console.log('首页数据', res);
      if (res) {
        homeData.value = res;
        // 处理轮播图数据
        processSlideshow(res.modules);

        // 保持原有的逻辑
        if (res.modules) {
          res.modules.forEach((module) => {
            detail.value[module.code] = module;
          });
        }
      }
    })
    .catch((error) => {
      console.error('获取首页数据失败:', error);
    });

  // 获取最新商品数据
  loadLatestProducts();
};

// 抽取获取最新商品的方法，便于重用
const loadLatestProducts = () => {
  GetLatestProducts()
    .then((res) => {
      console.log('最新商品数据', res);
      if (res && res.items) {
        // 根据设备类型显示不同数量的商品
        productList.value = res.items;
      }
    })
    .catch((error) => {
      console.error('获取商品数据失败:', error);
    });
};
</script>
<style lang="scss" scoped>
@import url('./index.scss');
</style>
